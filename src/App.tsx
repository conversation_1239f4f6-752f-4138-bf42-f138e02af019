import { useState } from "react";
import reactLogo from "./assets/react.svg";
import viteLogo from "/vite.svg";

function App() {
  const [count, setCount] = useState(0);
  const [message, setMessage] = useState("");

  const handleButtonClick = () => {
    setCount((count) => count + 1);
    setMessage(count === 9 ? "🎉 You've reached 10!" : "");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full bg-white rounded-3xl shadow-2xl p-8 space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center space-x-6 mb-6">
            <a
              href="https://vite.dev"
              target="_blank"
              className="group transition-transform hover:scale-110"
            >
              <img
                src={viteLogo}
                className="h-16 w-16 group-hover:drop-shadow-lg transition-all duration-300"
                alt="Vite logo"
              />
            </a>
            <a
              href="https://react.dev"
              target="_blank"
              className="group transition-transform hover:scale-110"
            >
              <img
                src={reactLogo}
                className="h-16 w-16 animate-spin-slow group-hover:drop-shadow-lg transition-all duration-300"
                alt="React logo"
              />
            </a>
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
            Vite + React + Tailwind v4
          </h1>
          <p className="text-gray-600 text-lg">
            A modern React development setup
          </p>
        </div>

        {/* Interactive Section */}
        <div className="bg-gray-50 rounded-2xl p-6 space-y-6">
          <div className="text-center">
            <button
              onClick={handleButtonClick}
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 px-8 rounded-full transition-all duration-300 transform hover:scale-105 hover:shadow-lg active:scale-95"
            >
              Count is {count}
            </button>

            {message && (
              <div className="mt-4 text-2xl animate-bounce">{message}</div>
            )}
          </div>

          <div className="border-t border-gray-200 pt-4">
            <p className="text-gray-600 text-center">
              Edit{" "}
              <code className="bg-gray-200 px-2 py-1 rounded text-sm font-mono">
                src/App.tsx
              </code>{" "}
              and save to test HMR
            </p>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 p-4 rounded-xl text-center">
            <div className="text-2xl mb-2">⚡</div>
            <h3 className="font-semibold text-blue-900">Lightning Fast</h3>
            <p className="text-blue-700 text-sm">Vite's instant HMR</p>
          </div>
          <div className="bg-green-50 p-4 rounded-xl text-center">
            <div className="text-2xl mb-2">🎨</div>
            <h3 className="font-semibold text-green-900">Beautiful UI</h3>
            <p className="text-green-700 text-sm">Tailwind CSS v4</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-xl text-center">
            <div className="text-2xl mb-2">🔧</div>
            <h3 className="font-semibold text-purple-900">Type Safe</h3>
            <p className="text-purple-700 text-sm">TypeScript Ready</p>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-gray-500 text-sm border-t border-gray-200 pt-6">
          Click on the Vite and React logos to learn more
        </div>
      </div>
    </div>
  );
}

export default App;
